2025-08-23 23:53:03,231 - INFO - 🚀 开始Ozon高级SKU差异分析...
2025-08-23 23:53:03,231 - INFO - 🔍 扫描目录: ozon 7月送仓
2025-08-23 23:53:03,231 - INFO - 📁 找到 29 个Excel文件
2025-08-23 23:53:03,232 - INFO - 📦 发货数据文件: 2000026537595-data.xlsx
2025-08-23 23:53:03,232 - INFO - 📦 发货数据文件: 2000026537789-data.xlsx
2025-08-23 23:53:03,232 - INFO - 📋 验收报告文件: supply-2000024950553-acceptance-report.xlsx
2025-08-23 23:53:03,232 - INFO - 📋 验收报告文件: supply-2000024950789-acceptance-report.xlsx
2025-08-23 23:53:03,232 - INFO - 📋 验收报告文件: supply-2000024953446-acceptance-report.xlsx
2025-08-23 23:53:03,237 - INFO - 📋 验收报告文件: supply-2000024953461-acceptance-report.xlsx
2025-08-23 23:53:03,237 - INFO - 📋 验收报告文件: supply-2000024953511-acceptance-report.xlsx
2025-08-23 23:53:03,237 - INFO - 📋 验收报告文件: supply-2000024953524-acceptance-report.xlsx
2025-08-23 23:53:03,238 - INFO - 📋 验收报告文件: supply-2000024990796-acceptance-report.xlsx
2025-08-23 23:53:03,238 - INFO - 📋 验收报告文件: supply-2000024992213-acceptance-report.xlsx
2025-08-23 23:53:03,238 - INFO - 📋 验收报告文件: supply-2000024992484-acceptance-report.xlsx
2025-08-23 23:53:03,238 - INFO - 📋 验收报告文件: supply-2000024993125-acceptance-report.xlsx
2025-08-23 23:53:03,238 - INFO - 📋 验收报告文件: supply-2000024994651-acceptance-report.xlsx
2025-08-23 23:53:03,238 - INFO - 📋 验收报告文件: supply-2000024995339-acceptance-report.xlsx
2025-08-23 23:53:03,238 - INFO - 📋 验收报告文件: supply-2000025090484-acceptance-report.xlsx
2025-08-23 23:53:03,238 - INFO - 📋 验收报告文件: supply-2000026412138-acceptance-report.xlsx
2025-08-23 23:53:03,238 - INFO - 📋 验收报告文件: supply-2000026412754-acceptance-report.xlsx
2025-08-23 23:53:03,238 - INFO - 📋 验收报告文件: supply-2000026441759-acceptance-report.xlsx
2025-08-23 23:53:03,239 - INFO - 📋 验收报告文件: supply-2000026444252-acceptance-report.xlsx
2025-08-23 23:53:03,239 - INFO - 📋 验收报告文件: supply-2000026444834-acceptance-report.xlsx
2025-08-23 23:53:03,239 - INFO - 📋 验收报告文件: supply-2000026445022-acceptance-report.xlsx
2025-08-23 23:53:03,239 - INFO - 📋 验收报告文件: supply-2000026445223-acceptance-report.xlsx
2025-08-23 23:53:03,239 - INFO - 📋 验收报告文件: supply-2000026445560-acceptance-report.xlsx
2025-08-23 23:53:03,239 - INFO - 📋 验收报告文件: supply-2000026445787-acceptance-report.xlsx
2025-08-23 23:53:03,239 - INFO - 📋 验收报告文件: supply-2000026446399-acceptance-report.xlsx
2025-08-23 23:53:03,239 - INFO - 📋 验收报告文件: supply-2000026446444-acceptance-report.xlsx
2025-08-23 23:53:03,239 - INFO - 📋 验收报告文件: supply-2000026646108-acceptance-report.xlsx
2025-08-23 23:53:03,240 - INFO - 📊 文件分类结果:
2025-08-23 23:53:03,240 - INFO -   - 发货数据文件: 2 个
2025-08-23 23:53:03,240 - INFO -   - 验收报告文件: 25 个
2025-08-23 23:53:03,240 - INFO - 
📦 开始处理 2 个发货数据文件...
2025-08-23 23:53:03,240 - INFO - 📄 处理发货数据文件: 2000026537595-data.xlsx
2025-08-23 23:53:03,490 - INFO -   📊 读取数据: 11 行, 7 列
2025-08-23 23:53:03,490 - INFO -   🔍 使用列: SKU='Артикул', 发货数量='В поставке (шт.)'
2025-08-23 23:53:03,492 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:53:03,492 - INFO -   📝 SKU示例: [('MLN101-1', 72), ('MLN102-2', 54), ('MLN101-3', 36)]
2025-08-23 23:53:03,492 - INFO - 📄 处理发货数据文件: 2000026537789-data.xlsx
2025-08-23 23:53:03,502 - INFO -   📊 读取数据: 12 行, 7 列
2025-08-23 23:53:03,502 - INFO -   🔍 使用列: SKU='Артикул', 发货数量='В поставке (шт.)'
2025-08-23 23:53:03,503 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:53:03,503 - INFO -   📝 SKU示例: [('MLN101-1', 144), ('MLN101-3', 48), ('MLN101-4', 36)]
2025-08-23 23:53:03,503 - INFO - 📦 发货数据文件处理完成，总计 16 个唯一SKU
2025-08-23 23:53:03,503 - INFO - 
📋 开始处理 25 个验收报告文件...
2025-08-23 23:53:03,504 - INFO - 📄 处理验收报告文件: supply-2000024950553-acceptance-report.xlsx
2025-08-23 23:53:03,518 - INFO -   📊 读取数据: 17 行, 14 列
2025-08-23 23:53:03,518 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,519 - INFO -   ✅ 提取完成: 有效行=17, 无效行=0, 唯一SKU=17
2025-08-23 23:53:03,519 - INFO -   📝 SKU示例: [('MLN01-3', (32, 32, 0)), ('MLN02-3', (20, 20, 0)), ('MLN101-1', (576, 573, -3))]
2025-08-23 23:53:03,519 - INFO - 📄 处理验收报告文件: supply-2000024950789-acceptance-report.xlsx
2025-08-23 23:53:03,531 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:53:03,531 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,532 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:53:03,532 - INFO -   📝 SKU示例: [('MLN101-5', (16, 16, 0)), ('MLN101-3', (36, 36, 0)), ('MLN101-1', (72, 72, 0))]
2025-08-23 23:53:03,532 - INFO - 📄 处理验收报告文件: supply-2000024953446-acceptance-report.xlsx
2025-08-23 23:53:03,546 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:53:03,547 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,547 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:53:03,548 - INFO -   📝 SKU示例: [('MLN102-2', (36, 36, 0)), ('MLN101-3', (60, 56, -4)), ('MNT100-1', (64, 64, 0))]
2025-08-23 23:53:03,548 - INFO - 📄 处理验收报告文件: supply-2000024953461-acceptance-report.xlsx
2025-08-23 23:53:03,560 - INFO -   📊 读取数据: 10 行, 14 列
2025-08-23 23:53:03,560 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,561 - INFO -   ✅ 提取完成: 有效行=10, 无效行=0, 唯一SKU=10
2025-08-23 23:53:03,561 - INFO -   📝 SKU示例: [('MLN01-1', (14, 14, 0)), ('MLN102-3', (36, 36, 0)), ('MLN102-4', (48, 48, 0))]
2025-08-23 23:53:03,561 - INFO - 📄 处理验收报告文件: supply-2000024953511-acceptance-report.xlsx
2025-08-23 23:53:03,574 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:53:03,574 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,575 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:53:03,575 - INFO -   📝 SKU示例: [('MLN102-4', (24, 24, 0)), ('MLN101-5', (24, 24, 0)), ('MLN101-3', (24, 24, 0))]
2025-08-23 23:53:03,575 - INFO - 📄 处理验收报告文件: supply-2000024953524-acceptance-report.xlsx
2025-08-23 23:53:03,588 - INFO -   📊 读取数据: 10 行, 14 列
2025-08-23 23:53:03,588 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,589 - INFO -   ✅ 提取完成: 有效行=10, 无效行=0, 唯一SKU=10
2025-08-23 23:53:03,589 - INFO -   📝 SKU示例: [('MLN102-2', (36, 36, 0)), ('MLN101-5', (32, 32, 0)), ('MLN101-6', (16, 16, 0))]
2025-08-23 23:53:03,589 - INFO - 📄 处理验收报告文件: supply-2000024990796-acceptance-report.xlsx
2025-08-23 23:53:03,602 - INFO -   📊 读取数据: 13 行, 14 列
2025-08-23 23:53:03,602 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,603 - INFO -   ✅ 提取完成: 有效行=13, 无效行=0, 唯一SKU=13
2025-08-23 23:53:03,603 - INFO -   📝 SKU示例: [('MLN101-1', (72, 72, 0)), ('MLN101-2', (16, 16, 0)), ('ODD100-1', (12, 12, 0))]
2025-08-23 23:53:03,604 - INFO - 📄 处理验收报告文件: supply-2000024992213-acceptance-report.xlsx
2025-08-23 23:53:03,617 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:53:03,617 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,618 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:53:03,618 - INFO -   📝 SKU示例: [('MLN101-3', (72, 72, 0)), ('MLN01-1', (14, 0, -14)), ('MLN102-4', (12, 12, 0))]
2025-08-23 23:53:03,618 - INFO - 📄 处理验收报告文件: supply-2000024992484-acceptance-report.xlsx
2025-08-23 23:53:03,631 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:53:03,631 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,632 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:53:03,632 - INFO -   📝 SKU示例: [('MLN101-1', (72, 72, 0)), ('MNT100-1', (24, 24, 0)), ('MLN102-3', (24, 24, 0))]
2025-08-23 23:53:03,632 - INFO - 📄 处理验收报告文件: supply-2000024993125-acceptance-report.xlsx
2025-08-23 23:53:03,644 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:53:03,644 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,645 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:53:03,645 - INFO -   📝 SKU示例: [('MLN102-2', (18, 18, 0)), ('MLN101-5', (16, 16, 0)), ('MLN101-3', (36, 36, 0))]
2025-08-23 23:53:03,645 - INFO - 📄 处理验收报告文件: supply-2000024994651-acceptance-report.xlsx
2025-08-23 23:53:03,659 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:53:03,659 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,660 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:53:03,660 - INFO -   📝 SKU示例: [('MLN101-1', (36, 36, 0)), ('MNT100-1', (8, 8, 0)), ('MLN101-2', (18, 18, 0))]
2025-08-23 23:53:03,660 - INFO - 📄 处理验收报告文件: supply-2000024995339-acceptance-report.xlsx
2025-08-23 23:53:03,673 - INFO -   📊 读取数据: 1 行, 14 列
2025-08-23 23:53:03,673 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,674 - INFO -   ✅ 提取完成: 有效行=1, 无效行=0, 唯一SKU=1
2025-08-23 23:53:03,674 - INFO -   📝 SKU示例: [('MLN108-1', (20, 20, 0))]
2025-08-23 23:53:03,674 - INFO - 📄 处理验收报告文件: supply-2000025090484-acceptance-report.xlsx
2025-08-23 23:53:03,698 - INFO -   📊 读取数据: 9 行, 14 列
2025-08-23 23:53:03,699 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,700 - INFO -   ✅ 提取完成: 有效行=9, 无效行=0, 唯一SKU=9
2025-08-23 23:53:03,700 - INFO -   📝 SKU示例: [('MLN101-4', (24, 24, 0)), ('MLN102-2', (54, 54, 0)), ('MLN101-3', (60, 60, 0))]
2025-08-23 23:53:03,700 - INFO - 📄 处理验收报告文件: supply-2000026412138-acceptance-report.xlsx
2025-08-23 23:53:03,715 - INFO -   📊 读取数据: 17 行, 14 列
2025-08-23 23:53:03,715 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,716 - INFO -   ✅ 提取完成: 有效行=17, 无效行=0, 唯一SKU=17
2025-08-23 23:53:03,716 - INFO -   📝 SKU示例: [('ODD100-1', (60, 60, 0)), ('MLN102-1', (90, 90, 0)), ('MLN101-1', (396, 396, 0))]
2025-08-23 23:53:03,716 - INFO - 📄 处理验收报告文件: supply-2000026412754-acceptance-report.xlsx
2025-08-23 23:53:03,730 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:53:03,731 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,731 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:53:03,731 - INFO -   📝 SKU示例: [('MLN102-4', (36, 36, 0)), ('MLN01-4', (8, 6, -2)), ('ODD100-2', (12, 12, 0))]
2025-08-23 23:53:03,732 - INFO - 📄 处理验收报告文件: supply-2000026441759-acceptance-report.xlsx
2025-08-23 23:53:03,744 - INFO -   📊 读取数据: 15 行, 14 列
2025-08-23 23:53:03,745 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,746 - INFO -   ✅ 提取完成: 有效行=15, 无效行=0, 唯一SKU=15
2025-08-23 23:53:03,746 - INFO -   📝 SKU示例: [('ODD100-1', (24, 24, 0)), ('MNT101-1', (20, 20, 0)), ('MLN101-3', (60, 60, 0))]
2025-08-23 23:53:03,746 - INFO - 📄 处理验收报告文件: supply-2000026444252-acceptance-report.xlsx
2025-08-23 23:53:03,760 - INFO -   📊 读取数据: 15 行, 14 列
2025-08-23 23:53:03,760 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,761 - INFO -   ✅ 提取完成: 有效行=15, 无效行=0, 唯一SKU=15
2025-08-23 23:53:03,761 - INFO -   📝 SKU示例: [('ODD100-1', (60, 60, 0)), ('MLN101-4', (12, 12, 0)), ('MLN102-3', (48, 48, 0))]
2025-08-23 23:53:03,761 - INFO - 📄 处理验收报告文件: supply-2000026444834-acceptance-report.xlsx
2025-08-23 23:53:03,775 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:53:03,775 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,776 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:53:03,776 - INFO -   📝 SKU示例: [('MLN01-3', (8, 8, 0)), ('MLN01-4', (8, 8, 0)), ('MNT101-1', (15, 15, 0))]
2025-08-23 23:53:03,776 - INFO - 📄 处理验收报告文件: supply-2000026445022-acceptance-report.xlsx
2025-08-23 23:53:03,790 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:53:03,790 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,791 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:53:03,791 - INFO -   📝 SKU示例: [('MLN101-2', (18, 18, 0)), ('MNT102-1', (12, 12, 0)), ('MLN01-3', (8, 8, 0))]
2025-08-23 23:53:03,791 - INFO - 📄 处理验收报告文件: supply-2000026445223-acceptance-report.xlsx
2025-08-23 23:53:03,805 - INFO -   📊 读取数据: 16 行, 14 列
2025-08-23 23:53:03,805 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,806 - INFO -   ✅ 提取完成: 有效行=16, 无效行=0, 唯一SKU=16
2025-08-23 23:53:03,806 - INFO -   📝 SKU示例: [('MNT102-1', (6, 6, 0)), ('MLN01-4', (8, 8, 0)), ('ODD100-2', (12, 11, -1))]
2025-08-23 23:53:03,807 - INFO - 📄 处理验收报告文件: supply-2000026445560-acceptance-report.xlsx
2025-08-23 23:53:03,821 - INFO -   📊 读取数据: 13 行, 14 列
2025-08-23 23:53:03,821 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,821 - INFO -   ✅ 提取完成: 有效行=13, 无效行=0, 唯一SKU=13
2025-08-23 23:53:03,821 - INFO -   📝 SKU示例: [('MLN101-3', (24, 24, 0)), ('MLN01-1', (14, 14, 0)), ('MLN102-4', (36, 36, 0))]
2025-08-23 23:53:03,821 - INFO - 📄 处理验收报告文件: supply-2000026445787-acceptance-report.xlsx
2025-08-23 23:53:03,835 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:53:03,835 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,836 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:53:03,836 - INFO -   📝 SKU示例: [('MLN01-2', (14, 14, 0)), ('MLN102-2', (36, 36, 0)), ('MLN101-3', (36, 36, 0))]
2025-08-23 23:53:03,836 - INFO - 📄 处理验收报告文件: supply-2000026446399-acceptance-report.xlsx
2025-08-23 23:53:03,847 - INFO -   📊 读取数据: 4 行, 14 列
2025-08-23 23:53:03,847 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,847 - INFO -   ✅ 提取完成: 有效行=4, 无效行=0, 唯一SKU=4
2025-08-23 23:53:03,848 - INFO -   📝 SKU示例: [('MLN101-5', (16, 16, 0)), ('MLN101-3', (12, 12, 0)), ('MLN101-1', (72, 72, 0))]
2025-08-23 23:53:03,848 - INFO - 📄 处理验收报告文件: supply-2000026446444-acceptance-report.xlsx
2025-08-23 23:53:03,862 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:53:03,862 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,863 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:53:03,863 - INFO -   📝 SKU示例: [('MLN102-3', (24, 24, 0)), ('MNT101-2', (10, 10, 0)), ('ODD100-1', (12, 12, 0))]
2025-08-23 23:53:03,863 - INFO - 📄 处理验收报告文件: supply-2000026646108-acceptance-report.xlsx
2025-08-23 23:53:03,874 - INFO -   📊 读取数据: 1 行, 14 列
2025-08-23 23:53:03,875 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:53:03,875 - INFO -   ✅ 提取完成: 有效行=1, 无效行=0, 唯一SKU=1
2025-08-23 23:53:03,875 - INFO -   📝 SKU示例: [('MLN108-1', (40, 40, 0))]
2025-08-23 23:53:03,875 - INFO - 📦 将验收报告中的发货数量合并到总发货数量中...
2025-08-23 23:53:03,876 - INFO - 📋 验收报告文件处理完成，总计 23 个唯一SKU
2025-08-23 23:53:03,876 - INFO - 📦 合并后总发货SKU数量: 23 个
2025-08-23 23:53:03,876 - INFO - 📊 差异数据SKU数量: 23 个
2025-08-23 23:53:03,876 - INFO - 
📊 生成汇总报告...
2025-08-23 23:53:03,877 - INFO - 总共发现 23 个唯一SKU
2025-08-23 23:53:03,877 - INFO - 📋 报告按预定义SKU顺序排列，预定义SKU: 23, 其他SKU: 0
2025-08-23 23:53:03,883 - INFO - 💾 保存报告到: ozon_advanced_sku_report_20250823_235303.xlsx
2025-08-23 23:53:03,905 - INFO - ✅ 报告保存成功
