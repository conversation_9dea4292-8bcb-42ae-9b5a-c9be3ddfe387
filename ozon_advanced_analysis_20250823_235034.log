2025-08-23 23:50:34,282 - INFO - 🚀 开始Ozon高级SKU差异分析...
2025-08-23 23:50:34,282 - INFO - 🔍 扫描目录: ozon 7月送仓
2025-08-23 23:50:34,282 - INFO - 📁 找到 29 个Excel文件
2025-08-23 23:50:34,282 - INFO - 📦 发货数据文件: 2000026537595-data.xlsx
2025-08-23 23:50:34,283 - INFO - 📦 发货数据文件: 2000026537789-data.xlsx
2025-08-23 23:50:34,283 - INFO - 📋 验收报告文件: supply-2000024950553-acceptance-report.xlsx
2025-08-23 23:50:34,283 - INFO - 📋 验收报告文件: supply-2000024950789-acceptance-report.xlsx
2025-08-23 23:50:34,283 - INFO - 📋 验收报告文件: supply-2000024953446-acceptance-report.xlsx
2025-08-23 23:50:34,289 - INFO - 📋 验收报告文件: supply-2000024953461-acceptance-report.xlsx
2025-08-23 23:50:34,289 - INFO - 📋 验收报告文件: supply-2000024953511-acceptance-report.xlsx
2025-08-23 23:50:34,289 - INFO - 📋 验收报告文件: supply-2000024953524-acceptance-report.xlsx
2025-08-23 23:50:34,289 - INFO - 📋 验收报告文件: supply-2000024990796-acceptance-report.xlsx
2025-08-23 23:50:34,290 - INFO - 📋 验收报告文件: supply-2000024992213-acceptance-report.xlsx
2025-08-23 23:50:34,290 - INFO - 📋 验收报告文件: supply-2000024992484-acceptance-report.xlsx
2025-08-23 23:50:34,290 - INFO - 📋 验收报告文件: supply-2000024993125-acceptance-report.xlsx
2025-08-23 23:50:34,290 - INFO - 📋 验收报告文件: supply-2000024994651-acceptance-report.xlsx
2025-08-23 23:50:34,291 - INFO - 📋 验收报告文件: supply-2000024995339-acceptance-report.xlsx
2025-08-23 23:50:34,291 - INFO - 📋 验收报告文件: supply-2000025090484-acceptance-report.xlsx
2025-08-23 23:50:34,291 - INFO - 📋 验收报告文件: supply-2000026412138-acceptance-report.xlsx
2025-08-23 23:50:34,291 - INFO - 📋 验收报告文件: supply-2000026412754-acceptance-report.xlsx
2025-08-23 23:50:34,291 - INFO - 📋 验收报告文件: supply-2000026441759-acceptance-report.xlsx
2025-08-23 23:50:34,292 - INFO - 📋 验收报告文件: supply-2000026444252-acceptance-report.xlsx
2025-08-23 23:50:34,292 - INFO - 📋 验收报告文件: supply-2000026444834-acceptance-report.xlsx
2025-08-23 23:50:34,292 - INFO - 📋 验收报告文件: supply-2000026445022-acceptance-report.xlsx
2025-08-23 23:50:34,292 - INFO - 📋 验收报告文件: supply-2000026445223-acceptance-report.xlsx
2025-08-23 23:50:34,293 - INFO - 📋 验收报告文件: supply-2000026445560-acceptance-report.xlsx
2025-08-23 23:50:34,293 - INFO - 📋 验收报告文件: supply-2000026445787-acceptance-report.xlsx
2025-08-23 23:50:34,293 - INFO - 📋 验收报告文件: supply-2000026446399-acceptance-report.xlsx
2025-08-23 23:50:34,293 - INFO - 📋 验收报告文件: supply-2000026446444-acceptance-report.xlsx
2025-08-23 23:50:34,294 - INFO - 📋 验收报告文件: supply-2000026646108-acceptance-report.xlsx
2025-08-23 23:50:34,294 - INFO - 📊 文件分类结果:
2025-08-23 23:50:34,294 - INFO -   - 发货数据文件: 2 个
2025-08-23 23:50:34,294 - INFO -   - 验收报告文件: 25 个
2025-08-23 23:50:34,294 - INFO - 
📦 开始处理 2 个发货数据文件...
2025-08-23 23:50:34,294 - INFO - 📄 处理发货数据文件: 2000026537595-data.xlsx
2025-08-23 23:50:34,547 - INFO -   📊 读取数据: 11 行, 7 列
2025-08-23 23:50:34,548 - INFO -   🔍 使用列: SKU='Артикул', 发货数量='В поставке (шт.)'
2025-08-23 23:50:34,548 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:50:34,548 - INFO -   📝 SKU示例: [('MLN101-1', 72), ('MLN102-2', 54), ('MLN101-3', 36)]
2025-08-23 23:50:34,548 - INFO - 📄 处理发货数据文件: 2000026537789-data.xlsx
2025-08-23 23:50:34,559 - INFO -   📊 读取数据: 12 行, 7 列
2025-08-23 23:50:34,559 - INFO -   🔍 使用列: SKU='Артикул', 发货数量='В поставке (шт.)'
2025-08-23 23:50:34,559 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:50:34,559 - INFO -   📝 SKU示例: [('MLN101-1', 144), ('MLN101-3', 48), ('MLN101-4', 36)]
2025-08-23 23:50:34,559 - INFO - 📦 发货数据文件处理完成，总计 16 个唯一SKU
2025-08-23 23:50:34,560 - INFO - 
📋 开始处理 25 个验收报告文件...
2025-08-23 23:50:34,560 - INFO - 📄 处理验收报告文件: supply-2000024950553-acceptance-report.xlsx
2025-08-23 23:50:34,576 - INFO -   📊 读取数据: 17 行, 14 列
2025-08-23 23:50:34,576 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,577 - INFO -   ✅ 提取完成: 有效行=17, 无效行=0, 唯一SKU=17
2025-08-23 23:50:34,577 - INFO -   📝 SKU示例: [('MLN01-3', (32, 32, 0)), ('MLN02-3', (20, 20, 0)), ('MLN101-1', (576, 573, -3))]
2025-08-23 23:50:34,577 - INFO - 📄 处理验收报告文件: supply-2000024950789-acceptance-report.xlsx
2025-08-23 23:50:34,589 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:50:34,590 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,590 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:50:34,590 - INFO -   📝 SKU示例: [('MLN101-5', (16, 16, 0)), ('MLN101-3', (36, 36, 0)), ('MLN101-1', (72, 72, 0))]
2025-08-23 23:50:34,591 - INFO - 📄 处理验收报告文件: supply-2000024953446-acceptance-report.xlsx
2025-08-23 23:50:34,604 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:50:34,604 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,605 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:50:34,605 - INFO -   📝 SKU示例: [('MLN102-2', (36, 36, 0)), ('MLN101-3', (60, 56, -4)), ('MNT100-1', (64, 64, 0))]
2025-08-23 23:50:34,606 - INFO - 📄 处理验收报告文件: supply-2000024953461-acceptance-report.xlsx
2025-08-23 23:50:34,618 - INFO -   📊 读取数据: 10 行, 14 列
2025-08-23 23:50:34,618 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,619 - INFO -   ✅ 提取完成: 有效行=10, 无效行=0, 唯一SKU=10
2025-08-23 23:50:34,619 - INFO -   📝 SKU示例: [('MLN01-1', (14, 14, 0)), ('MLN102-3', (36, 36, 0)), ('MLN102-4', (48, 48, 0))]
2025-08-23 23:50:34,619 - INFO - 📄 处理验收报告文件: supply-2000024953511-acceptance-report.xlsx
2025-08-23 23:50:34,635 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:50:34,635 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,635 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:50:34,636 - INFO -   📝 SKU示例: [('MLN102-4', (24, 24, 0)), ('MLN101-5', (24, 24, 0)), ('MLN101-3', (24, 24, 0))]
2025-08-23 23:50:34,636 - INFO - 📄 处理验收报告文件: supply-2000024953524-acceptance-report.xlsx
2025-08-23 23:50:34,649 - INFO -   📊 读取数据: 10 行, 14 列
2025-08-23 23:50:34,649 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,650 - INFO -   ✅ 提取完成: 有效行=10, 无效行=0, 唯一SKU=10
2025-08-23 23:50:34,650 - INFO -   📝 SKU示例: [('MLN102-2', (36, 36, 0)), ('MLN101-5', (32, 32, 0)), ('MLN101-6', (16, 16, 0))]
2025-08-23 23:50:34,650 - INFO - 📄 处理验收报告文件: supply-2000024990796-acceptance-report.xlsx
2025-08-23 23:50:34,662 - INFO -   📊 读取数据: 13 行, 14 列
2025-08-23 23:50:34,663 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,664 - INFO -   ✅ 提取完成: 有效行=13, 无效行=0, 唯一SKU=13
2025-08-23 23:50:34,664 - INFO -   📝 SKU示例: [('MLN101-1', (72, 72, 0)), ('MLN101-2', (16, 16, 0)), ('ODD100-1', (12, 12, 0))]
2025-08-23 23:50:34,664 - INFO - 📄 处理验收报告文件: supply-2000024992213-acceptance-report.xlsx
2025-08-23 23:50:34,677 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:50:34,678 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,679 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:50:34,679 - INFO -   📝 SKU示例: [('MLN101-3', (72, 72, 0)), ('MLN01-1', (14, 0, -14)), ('MLN102-4', (12, 12, 0))]
2025-08-23 23:50:34,679 - INFO - 📄 处理验收报告文件: supply-2000024992484-acceptance-report.xlsx
2025-08-23 23:50:34,690 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:50:34,690 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,691 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:50:34,691 - INFO -   📝 SKU示例: [('MLN101-1', (72, 72, 0)), ('MNT100-1', (24, 24, 0)), ('MLN102-3', (24, 24, 0))]
2025-08-23 23:50:34,692 - INFO - 📄 处理验收报告文件: supply-2000024993125-acceptance-report.xlsx
2025-08-23 23:50:34,704 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:50:34,704 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,704 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:50:34,705 - INFO -   📝 SKU示例: [('MLN102-2', (18, 18, 0)), ('MLN101-5', (16, 16, 0)), ('MLN101-3', (36, 36, 0))]
2025-08-23 23:50:34,705 - INFO - 📄 处理验收报告文件: supply-2000024994651-acceptance-report.xlsx
2025-08-23 23:50:34,718 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:50:34,718 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,719 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:50:34,719 - INFO -   📝 SKU示例: [('MLN101-1', (36, 36, 0)), ('MNT100-1', (8, 8, 0)), ('MLN101-2', (18, 18, 0))]
2025-08-23 23:50:34,720 - INFO - 📄 处理验收报告文件: supply-2000024995339-acceptance-report.xlsx
2025-08-23 23:50:34,731 - INFO -   📊 读取数据: 1 行, 14 列
2025-08-23 23:50:34,732 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,732 - INFO -   ✅ 提取完成: 有效行=1, 无效行=0, 唯一SKU=1
2025-08-23 23:50:34,732 - INFO -   📝 SKU示例: [('MLN108-1', (20, 20, 0))]
2025-08-23 23:50:34,732 - INFO - 📄 处理验收报告文件: supply-2000025090484-acceptance-report.xlsx
2025-08-23 23:50:34,758 - INFO -   📊 读取数据: 9 行, 14 列
2025-08-23 23:50:34,758 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,759 - INFO -   ✅ 提取完成: 有效行=9, 无效行=0, 唯一SKU=9
2025-08-23 23:50:34,759 - INFO -   📝 SKU示例: [('MLN101-4', (24, 24, 0)), ('MLN102-2', (54, 54, 0)), ('MLN101-3', (60, 60, 0))]
2025-08-23 23:50:34,759 - INFO - 📄 处理验收报告文件: supply-2000026412138-acceptance-report.xlsx
2025-08-23 23:50:34,773 - INFO -   📊 读取数据: 17 行, 14 列
2025-08-23 23:50:34,773 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,774 - INFO -   ✅ 提取完成: 有效行=17, 无效行=0, 唯一SKU=17
2025-08-23 23:50:34,774 - INFO -   📝 SKU示例: [('ODD100-1', (60, 60, 0)), ('MLN102-1', (90, 90, 0)), ('MLN101-1', (396, 396, 0))]
2025-08-23 23:50:34,774 - INFO - 📄 处理验收报告文件: supply-2000026412754-acceptance-report.xlsx
2025-08-23 23:50:34,787 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:50:34,788 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,788 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:50:34,788 - INFO -   📝 SKU示例: [('MLN102-4', (36, 36, 0)), ('MLN01-4', (8, 6, -2)), ('ODD100-2', (12, 12, 0))]
2025-08-23 23:50:34,789 - INFO - 📄 处理验收报告文件: supply-2000026441759-acceptance-report.xlsx
2025-08-23 23:50:34,801 - INFO -   📊 读取数据: 15 行, 14 列
2025-08-23 23:50:34,801 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,802 - INFO -   ✅ 提取完成: 有效行=15, 无效行=0, 唯一SKU=15
2025-08-23 23:50:34,802 - INFO -   📝 SKU示例: [('ODD100-1', (24, 24, 0)), ('MNT101-1', (20, 20, 0)), ('MLN101-3', (60, 60, 0))]
2025-08-23 23:50:34,803 - INFO - 📄 处理验收报告文件: supply-2000026444252-acceptance-report.xlsx
2025-08-23 23:50:34,814 - INFO -   📊 读取数据: 15 行, 14 列
2025-08-23 23:50:34,815 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,815 - INFO -   ✅ 提取完成: 有效行=15, 无效行=0, 唯一SKU=15
2025-08-23 23:50:34,816 - INFO -   📝 SKU示例: [('ODD100-1', (60, 60, 0)), ('MLN101-4', (12, 12, 0)), ('MLN102-3', (48, 48, 0))]
2025-08-23 23:50:34,816 - INFO - 📄 处理验收报告文件: supply-2000026444834-acceptance-report.xlsx
2025-08-23 23:50:34,828 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:50:34,828 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,828 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:50:34,829 - INFO -   📝 SKU示例: [('MLN01-3', (8, 8, 0)), ('MLN01-4', (8, 8, 0)), ('MNT101-1', (15, 15, 0))]
2025-08-23 23:50:34,829 - INFO - 📄 处理验收报告文件: supply-2000026445022-acceptance-report.xlsx
2025-08-23 23:50:34,843 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:50:34,843 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,844 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:50:34,844 - INFO -   📝 SKU示例: [('MLN101-2', (18, 18, 0)), ('MNT102-1', (12, 12, 0)), ('MLN01-3', (8, 8, 0))]
2025-08-23 23:50:34,844 - INFO - 📄 处理验收报告文件: supply-2000026445223-acceptance-report.xlsx
2025-08-23 23:50:34,858 - INFO -   📊 读取数据: 16 行, 14 列
2025-08-23 23:50:34,858 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,858 - INFO -   ✅ 提取完成: 有效行=16, 无效行=0, 唯一SKU=16
2025-08-23 23:50:34,859 - INFO -   📝 SKU示例: [('MNT102-1', (6, 6, 0)), ('MLN01-4', (8, 8, 0)), ('ODD100-2', (12, 11, -1))]
2025-08-23 23:50:34,859 - INFO - 📄 处理验收报告文件: supply-2000026445560-acceptance-report.xlsx
2025-08-23 23:50:34,872 - INFO -   📊 读取数据: 13 行, 14 列
2025-08-23 23:50:34,872 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,873 - INFO -   ✅ 提取完成: 有效行=13, 无效行=0, 唯一SKU=13
2025-08-23 23:50:34,873 - INFO -   📝 SKU示例: [('MLN101-3', (24, 24, 0)), ('MLN01-1', (14, 14, 0)), ('MLN102-4', (36, 36, 0))]
2025-08-23 23:50:34,873 - INFO - 📄 处理验收报告文件: supply-2000026445787-acceptance-report.xlsx
2025-08-23 23:50:34,888 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:50:34,888 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,889 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:50:34,889 - INFO -   📝 SKU示例: [('MLN01-2', (14, 14, 0)), ('MLN102-2', (36, 36, 0)), ('MLN101-3', (36, 36, 0))]
2025-08-23 23:50:34,889 - INFO - 📄 处理验收报告文件: supply-2000026446399-acceptance-report.xlsx
2025-08-23 23:50:34,900 - INFO -   📊 读取数据: 4 行, 14 列
2025-08-23 23:50:34,900 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,901 - INFO -   ✅ 提取完成: 有效行=4, 无效行=0, 唯一SKU=4
2025-08-23 23:50:34,901 - INFO -   📝 SKU示例: [('MLN101-5', (16, 16, 0)), ('MLN101-3', (12, 12, 0)), ('MLN101-1', (72, 72, 0))]
2025-08-23 23:50:34,901 - INFO - 📄 处理验收报告文件: supply-2000026446444-acceptance-report.xlsx
2025-08-23 23:50:34,914 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:50:34,915 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,915 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:50:34,916 - INFO -   📝 SKU示例: [('MLN102-3', (24, 24, 0)), ('MNT101-2', (10, 10, 0)), ('ODD100-1', (12, 12, 0))]
2025-08-23 23:50:34,916 - INFO - 📄 处理验收报告文件: supply-2000026646108-acceptance-report.xlsx
2025-08-23 23:50:34,926 - INFO -   📊 读取数据: 1 行, 14 列
2025-08-23 23:50:34,926 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)', 差异='Разница по поставке'
2025-08-23 23:50:34,926 - INFO -   ✅ 提取完成: 有效行=1, 无效行=0, 唯一SKU=1
2025-08-23 23:50:34,927 - INFO -   📝 SKU示例: [('MLN108-1', (40, 40, 0))]
2025-08-23 23:50:34,927 - INFO - 📦 将验收报告中的发货数量合并到总发货数量中...
2025-08-23 23:50:34,927 - INFO - 📋 验收报告文件处理完成，总计 23 个唯一SKU
2025-08-23 23:50:34,927 - INFO - 📦 合并后总发货SKU数量: 23 个
2025-08-23 23:50:34,927 - INFO - 📊 差异数据SKU数量: 23 个
2025-08-23 23:50:34,927 - INFO - 
📊 生成汇总报告...
2025-08-23 23:50:34,927 - INFO - 总共发现 23 个唯一SKU
2025-08-23 23:50:34,932 - INFO - 💾 保存报告到: ozon_advanced_sku_report_20250823_235034.xlsx
2025-08-23 23:50:34,959 - INFO - ✅ 报告保存成功
