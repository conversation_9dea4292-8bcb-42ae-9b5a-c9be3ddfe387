2025-08-23 23:43:30,217 - INFO - 🚀 开始Ozon高级SKU差异分析...
2025-08-23 23:43:30,218 - INFO - 🔍 扫描目录: ozon 7月送仓
2025-08-23 23:43:30,219 - INFO - 📁 找到 30 个Excel文件
2025-08-23 23:43:30,219 - INFO - 📦 发货数据文件: 2000026537595-data.xlsx
2025-08-23 23:43:30,219 - INFO - 📦 发货数据文件: 2000026537789-data.xlsx
2025-08-23 23:43:30,219 - INFO - 📋 验收报告文件: supply-2000024950553-acceptance-report.xlsx
2025-08-23 23:43:30,220 - INFO - 📋 验收报告文件: supply-2000024950789-acceptance-report.xlsx
2025-08-23 23:43:30,220 - INFO - 📋 验收报告文件: supply-2000024953446-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000024953461-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000024953511-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000024953524-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000024990796-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000024992213-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000024992484-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000024993125-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000024994651-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000024995339-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000025090484-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000026412138-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000026412754-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000026441759-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000026444252-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000026444834-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000026445022-acceptance-report.xlsx
2025-08-23 23:43:30,225 - INFO - 📋 验收报告文件: supply-2000026445223-acceptance-report.xlsx
2025-08-23 23:43:30,227 - INFO - 📋 验收报告文件: supply-2000026445560-acceptance-report.xlsx
2025-08-23 23:43:30,227 - INFO - 📋 验收报告文件: supply-2000026445787-acceptance-report.xlsx
2025-08-23 23:43:30,227 - INFO - 📋 验收报告文件: supply-2000026446399-acceptance-report.xlsx
2025-08-23 23:43:30,227 - INFO - 📋 验收报告文件: supply-2000026446444-acceptance-report.xlsx
2025-08-23 23:43:30,227 - INFO - 📋 验收报告文件: supply-2000026646108-acceptance-report.xlsx
2025-08-23 23:43:30,227 - INFO - 📊 文件分类结果:
2025-08-23 23:43:30,227 - INFO -   - 发货数据文件: 2 个
2025-08-23 23:43:30,227 - INFO -   - 验收报告文件: 25 个
2025-08-23 23:43:30,227 - INFO - 
📦 开始处理 2 个发货数据文件...
2025-08-23 23:43:30,227 - INFO - 📄 处理发货数据文件: 2000026537595-data.xlsx
2025-08-23 23:43:30,473 - INFO -   📊 读取数据: 11 行, 7 列
2025-08-23 23:43:30,474 - INFO -   🔍 使用列: SKU='Артикул', 发货数量='В поставке (шт.)'
2025-08-23 23:43:30,474 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:43:30,475 - INFO -   📝 SKU示例: [('MLN101-1', 72), ('MLN102-2', 54), ('MLN101-3', 36)]
2025-08-23 23:43:30,475 - INFO - 📄 处理发货数据文件: 2000026537789-data.xlsx
2025-08-23 23:43:30,485 - INFO -   📊 读取数据: 12 行, 7 列
2025-08-23 23:43:30,486 - INFO -   🔍 使用列: SKU='Артикул', 发货数量='В поставке (шт.)'
2025-08-23 23:43:30,486 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:43:30,486 - INFO -   📝 SKU示例: [('MLN101-1', 144), ('MLN101-3', 48), ('MLN101-4', 36)]
2025-08-23 23:43:30,487 - INFO - 📦 发货数据文件处理完成，总计 16 个唯一SKU
2025-08-23 23:43:30,487 - INFO - 
📋 开始处理 25 个验收报告文件...
2025-08-23 23:43:30,487 - INFO - 📄 处理验收报告文件: supply-2000024950553-acceptance-report.xlsx
2025-08-23 23:43:30,501 - INFO -   📊 读取数据: 17 行, 14 列
2025-08-23 23:43:30,501 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,502 - INFO -   ✅ 提取完成: 有效行=17, 无效行=0, 唯一SKU=17
2025-08-23 23:43:30,502 - INFO -   📝 SKU示例: [('MLN01-3', (32, 32)), ('MLN02-3', (20, 20)), ('MLN101-1', (576, 573))]
2025-08-23 23:43:30,502 - INFO - 📄 处理验收报告文件: supply-2000024950789-acceptance-report.xlsx
2025-08-23 23:43:30,514 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:43:30,514 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,514 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:43:30,515 - INFO -   📝 SKU示例: [('MLN101-5', (16, 16)), ('MLN101-3', (36, 36)), ('MLN101-1', (72, 72))]
2025-08-23 23:43:30,516 - INFO - 📄 处理验收报告文件: supply-2000024953446-acceptance-report.xlsx
2025-08-23 23:43:30,527 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:43:30,528 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,528 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:43:30,528 - INFO -   📝 SKU示例: [('MLN102-2', (36, 36)), ('MLN101-3', (60, 56)), ('MNT100-1', (64, 64))]
2025-08-23 23:43:30,529 - INFO - 📄 处理验收报告文件: supply-2000024953461-acceptance-report.xlsx
2025-08-23 23:43:30,541 - INFO -   📊 读取数据: 10 行, 14 列
2025-08-23 23:43:30,541 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,542 - INFO -   ✅ 提取完成: 有效行=10, 无效行=0, 唯一SKU=10
2025-08-23 23:43:30,542 - INFO -   📝 SKU示例: [('MLN01-1', (14, 14)), ('MLN102-3', (36, 36)), ('MLN102-4', (48, 48))]
2025-08-23 23:43:30,542 - INFO - 📄 处理验收报告文件: supply-2000024953511-acceptance-report.xlsx
2025-08-23 23:43:30,554 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:43:30,554 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,555 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:43:30,555 - INFO -   📝 SKU示例: [('MLN102-4', (24, 24)), ('MLN101-5', (24, 24)), ('MLN101-3', (24, 24))]
2025-08-23 23:43:30,555 - INFO - 📄 处理验收报告文件: supply-2000024953524-acceptance-report.xlsx
2025-08-23 23:43:30,568 - INFO -   📊 读取数据: 10 行, 14 列
2025-08-23 23:43:30,568 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,569 - INFO -   ✅ 提取完成: 有效行=10, 无效行=0, 唯一SKU=10
2025-08-23 23:43:30,569 - INFO -   📝 SKU示例: [('MLN102-2', (36, 36)), ('MLN101-5', (32, 32)), ('MLN101-6', (16, 16))]
2025-08-23 23:43:30,569 - INFO - 📄 处理验收报告文件: supply-2000024990796-acceptance-report.xlsx
2025-08-23 23:43:30,582 - INFO -   📊 读取数据: 13 行, 14 列
2025-08-23 23:43:30,582 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,583 - INFO -   ✅ 提取完成: 有效行=13, 无效行=0, 唯一SKU=13
2025-08-23 23:43:30,583 - INFO -   📝 SKU示例: [('MLN101-1', (72, 72)), ('MLN101-2', (16, 16)), ('ODD100-1', (12, 12))]
2025-08-23 23:43:30,583 - INFO - 📄 处理验收报告文件: supply-2000024992213-acceptance-report.xlsx
2025-08-23 23:43:30,596 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:43:30,597 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,597 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:43:30,598 - INFO -   📝 SKU示例: [('MLN101-3', (72, 72)), ('MLN01-1', (14, 0)), ('MLN102-4', (12, 12))]
2025-08-23 23:43:30,598 - INFO - 📄 处理验收报告文件: supply-2000024992484-acceptance-report.xlsx
2025-08-23 23:43:30,609 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:43:30,609 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,609 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:43:30,610 - INFO -   📝 SKU示例: [('MLN101-1', (72, 72)), ('MNT100-1', (24, 24)), ('MLN102-3', (24, 24))]
2025-08-23 23:43:30,611 - INFO - 📄 处理验收报告文件: supply-2000024993125-acceptance-report.xlsx
2025-08-23 23:43:30,622 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:43:30,622 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,623 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:43:30,623 - INFO -   📝 SKU示例: [('MLN102-2', (18, 18)), ('MLN101-5', (16, 16)), ('MLN101-3', (36, 36))]
2025-08-23 23:43:30,623 - INFO - 📄 处理验收报告文件: supply-2000024994651-acceptance-report.xlsx
2025-08-23 23:43:30,635 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:43:30,635 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,636 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:43:30,636 - INFO -   📝 SKU示例: [('MLN101-1', (36, 36)), ('MNT100-1', (8, 8)), ('MLN101-2', (18, 18))]
2025-08-23 23:43:30,636 - INFO - 📄 处理验收报告文件: supply-2000024995339-acceptance-report.xlsx
2025-08-23 23:43:30,648 - INFO -   📊 读取数据: 1 行, 14 列
2025-08-23 23:43:30,648 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,648 - INFO -   ✅ 提取完成: 有效行=1, 无效行=0, 唯一SKU=1
2025-08-23 23:43:30,648 - INFO -   📝 SKU示例: [('MLN108-1', (20, 20))]
2025-08-23 23:43:30,648 - INFO - 📄 处理验收报告文件: supply-2000025090484-acceptance-report.xlsx
2025-08-23 23:43:30,670 - INFO -   📊 读取数据: 9 行, 14 列
2025-08-23 23:43:30,671 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,671 - INFO -   ✅ 提取完成: 有效行=9, 无效行=0, 唯一SKU=9
2025-08-23 23:43:30,671 - INFO -   📝 SKU示例: [('MLN101-4', (24, 24)), ('MLN102-2', (54, 54)), ('MLN101-3', (60, 60))]
2025-08-23 23:43:30,671 - INFO - 📄 处理验收报告文件: supply-2000026412138-acceptance-report.xlsx
2025-08-23 23:43:30,683 - INFO -   📊 读取数据: 17 行, 14 列
2025-08-23 23:43:30,684 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,685 - INFO -   ✅ 提取完成: 有效行=17, 无效行=0, 唯一SKU=17
2025-08-23 23:43:30,685 - INFO -   📝 SKU示例: [('ODD100-1', (60, 60)), ('MLN102-1', (90, 90)), ('MLN101-1', (396, 396))]
2025-08-23 23:43:30,685 - INFO - 📄 处理验收报告文件: supply-2000026412754-acceptance-report.xlsx
2025-08-23 23:43:30,697 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:43:30,697 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,698 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:43:30,698 - INFO -   📝 SKU示例: [('MLN102-4', (36, 36)), ('MLN01-4', (8, 6)), ('ODD100-2', (12, 12))]
2025-08-23 23:43:30,698 - INFO - 📄 处理验收报告文件: supply-2000026441759-acceptance-report.xlsx
2025-08-23 23:43:30,712 - INFO -   📊 读取数据: 15 行, 14 列
2025-08-23 23:43:30,712 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,713 - INFO -   ✅ 提取完成: 有效行=15, 无效行=0, 唯一SKU=15
2025-08-23 23:43:30,714 - INFO -   📝 SKU示例: [('ODD100-1', (24, 24)), ('MNT101-1', (20, 20)), ('MLN101-3', (60, 60))]
2025-08-23 23:43:30,714 - INFO - 📄 处理验收报告文件: supply-2000026444252-acceptance-report.xlsx
2025-08-23 23:43:30,728 - INFO -   📊 读取数据: 15 行, 14 列
2025-08-23 23:43:30,729 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,730 - INFO -   ✅ 提取完成: 有效行=15, 无效行=0, 唯一SKU=15
2025-08-23 23:43:30,730 - INFO -   📝 SKU示例: [('ODD100-1', (60, 60)), ('MLN101-4', (12, 12)), ('MLN102-3', (48, 48))]
2025-08-23 23:43:30,730 - INFO - 📄 处理验收报告文件: supply-2000026444834-acceptance-report.xlsx
2025-08-23 23:43:30,745 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:43:30,745 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,745 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:43:30,745 - INFO -   📝 SKU示例: [('MLN01-3', (8, 8)), ('MLN01-4', (8, 8)), ('MNT101-1', (15, 15))]
2025-08-23 23:43:30,746 - INFO - 📄 处理验收报告文件: supply-2000026445022-acceptance-report.xlsx
2025-08-23 23:43:30,760 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:43:30,760 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,760 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:43:30,761 - INFO -   📝 SKU示例: [('MLN101-2', (18, 18)), ('MNT102-1', (12, 12)), ('MLN01-3', (8, 8))]
2025-08-23 23:43:30,761 - INFO - 📄 处理验收报告文件: supply-2000026445223-acceptance-report.xlsx
2025-08-23 23:43:30,775 - INFO -   📊 读取数据: 16 行, 14 列
2025-08-23 23:43:30,775 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,777 - INFO -   ✅ 提取完成: 有效行=16, 无效行=0, 唯一SKU=16
2025-08-23 23:43:30,777 - INFO -   📝 SKU示例: [('MNT102-1', (6, 6)), ('MLN01-4', (8, 8)), ('ODD100-2', (12, 11))]
2025-08-23 23:43:30,777 - INFO - 📄 处理验收报告文件: supply-2000026445560-acceptance-report.xlsx
2025-08-23 23:43:30,790 - INFO -   📊 读取数据: 13 行, 14 列
2025-08-23 23:43:30,790 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,791 - INFO -   ✅ 提取完成: 有效行=13, 无效行=0, 唯一SKU=13
2025-08-23 23:43:30,791 - INFO -   📝 SKU示例: [('MLN101-3', (24, 24)), ('MLN01-1', (14, 14)), ('MLN102-4', (36, 36))]
2025-08-23 23:43:30,791 - INFO - 📄 处理验收报告文件: supply-2000026445787-acceptance-report.xlsx
2025-08-23 23:43:30,804 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:43:30,804 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,806 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:43:30,806 - INFO -   📝 SKU示例: [('MLN01-2', (14, 14)), ('MLN102-2', (36, 36)), ('MLN101-3', (36, 36))]
2025-08-23 23:43:30,806 - INFO - 📄 处理验收报告文件: supply-2000026446399-acceptance-report.xlsx
2025-08-23 23:43:30,817 - INFO -   📊 读取数据: 4 行, 14 列
2025-08-23 23:43:30,817 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,818 - INFO -   ✅ 提取完成: 有效行=4, 无效行=0, 唯一SKU=4
2025-08-23 23:43:30,818 - INFO -   📝 SKU示例: [('MLN101-5', (16, 16)), ('MLN101-3', (12, 12)), ('MLN101-1', (72, 72))]
2025-08-23 23:43:30,818 - INFO - 📄 处理验收报告文件: supply-2000026446444-acceptance-report.xlsx
2025-08-23 23:43:30,830 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:43:30,831 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,831 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:43:30,832 - INFO -   📝 SKU示例: [('MLN102-3', (24, 24)), ('MNT101-2', (10, 10)), ('ODD100-1', (12, 12))]
2025-08-23 23:43:30,832 - INFO - 📄 处理验收报告文件: supply-2000026646108-acceptance-report.xlsx
2025-08-23 23:43:30,843 - INFO -   📊 读取数据: 1 行, 14 列
2025-08-23 23:43:30,843 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:43:30,844 - INFO -   ✅ 提取完成: 有效行=1, 无效行=0, 唯一SKU=1
2025-08-23 23:43:30,844 - INFO -   📝 SKU示例: [('MLN108-1', (40, 40))]
2025-08-23 23:43:30,844 - INFO - 📦 将验收报告中的发货数量合并到总发货数量中...
2025-08-23 23:43:30,844 - INFO - 📋 验收报告文件处理完成，总计 23 个唯一SKU
2025-08-23 23:43:30,845 - INFO - 📦 合并后总发货SKU数量: 23 个
2025-08-23 23:43:30,845 - INFO - 
📊 生成汇总报告...
2025-08-23 23:43:30,845 - INFO - 总共发现 23 个唯一SKU
2025-08-23 23:43:30,852 - INFO - 💾 保存报告到: ozon_advanced_sku_report_20250823_234330.xlsx
2025-08-23 23:43:30,878 - INFO - ✅ 报告保存成功
