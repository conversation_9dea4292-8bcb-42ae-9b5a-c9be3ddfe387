2025-08-23 23:39:43,150 - INFO - 🚀 开始Ozon高级SKU差异分析...
2025-08-23 23:39:43,151 - INFO - 🔍 扫描目录: ozon 7月送仓
2025-08-23 23:39:43,151 - INFO - 📁 找到 29 个Excel文件
2025-08-23 23:39:43,151 - INFO - 📦 发货数据文件: 2000026537595-data.xlsx
2025-08-23 23:39:43,152 - INFO - 📦 发货数据文件: 2000026537789-data.xlsx
2025-08-23 23:39:43,152 - INFO - 📋 验收报告文件: supply-2000024950553-acceptance-report.xlsx
2025-08-23 23:39:43,152 - INFO - 📋 验收报告文件: supply-2000024950789-acceptance-report.xlsx
2025-08-23 23:39:43,152 - INFO - 📋 验收报告文件: supply-2000024953446-acceptance-report.xlsx
2025-08-23 23:39:43,157 - INFO - 📋 验收报告文件: supply-2000024953461-acceptance-report.xlsx
2025-08-23 23:39:43,157 - INFO - 📋 验收报告文件: supply-2000024953511-acceptance-report.xlsx
2025-08-23 23:39:43,157 - INFO - 📋 验收报告文件: supply-2000024953524-acceptance-report.xlsx
2025-08-23 23:39:43,157 - INFO - 📋 验收报告文件: supply-2000024990796-acceptance-report.xlsx
2025-08-23 23:39:43,157 - INFO - 📋 验收报告文件: supply-2000024992213-acceptance-report.xlsx
2025-08-23 23:39:43,159 - INFO - 📋 验收报告文件: supply-2000024992484-acceptance-report.xlsx
2025-08-23 23:39:43,159 - INFO - 📋 验收报告文件: supply-2000024993125-acceptance-report.xlsx
2025-08-23 23:39:43,159 - INFO - 📋 验收报告文件: supply-2000024994651-acceptance-report.xlsx
2025-08-23 23:39:43,159 - INFO - 📋 验收报告文件: supply-2000024995339-acceptance-report.xlsx
2025-08-23 23:39:43,159 - INFO - 📋 验收报告文件: supply-2000025090484-acceptance-report.xlsx
2025-08-23 23:39:43,159 - INFO - 📋 验收报告文件: supply-2000026412138-acceptance-report.xlsx
2025-08-23 23:39:43,159 - INFO - 📋 验收报告文件: supply-2000026412754-acceptance-report.xlsx
2025-08-23 23:39:43,159 - INFO - 📋 验收报告文件: supply-2000026441759-acceptance-report.xlsx
2025-08-23 23:39:43,159 - INFO - 📋 验收报告文件: supply-2000026444252-acceptance-report.xlsx
2025-08-23 23:39:43,160 - INFO - 📋 验收报告文件: supply-2000026444834-acceptance-report.xlsx
2025-08-23 23:39:43,160 - INFO - 📋 验收报告文件: supply-2000026445022-acceptance-report.xlsx
2025-08-23 23:39:43,160 - INFO - 📋 验收报告文件: supply-2000026445223-acceptance-report.xlsx
2025-08-23 23:39:43,160 - INFO - 📋 验收报告文件: supply-2000026445560-acceptance-report.xlsx
2025-08-23 23:39:43,160 - INFO - 📋 验收报告文件: supply-2000026445787-acceptance-report.xlsx
2025-08-23 23:39:43,160 - INFO - 📋 验收报告文件: supply-2000026446399-acceptance-report.xlsx
2025-08-23 23:39:43,160 - INFO - 📋 验收报告文件: supply-2000026446444-acceptance-report.xlsx
2025-08-23 23:39:43,161 - INFO - 📋 验收报告文件: supply-2000026646108-acceptance-report.xlsx
2025-08-23 23:39:43,161 - INFO - 📊 文件分类结果:
2025-08-23 23:39:43,161 - INFO -   - 发货数据文件: 2 个
2025-08-23 23:39:43,161 - INFO -   - 验收报告文件: 25 个
2025-08-23 23:39:43,161 - INFO - 
📦 开始处理 2 个发货数据文件...
2025-08-23 23:39:43,161 - INFO - 📄 处理发货数据文件: 2000026537595-data.xlsx
2025-08-23 23:39:43,409 - INFO -   📊 读取数据: 11 行, 7 列
2025-08-23 23:39:43,409 - INFO -   🔍 使用列: SKU='Артикул', 发货数量='В поставке (шт.)'
2025-08-23 23:39:43,411 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:39:43,411 - INFO -   📝 SKU示例: [('MLN101-1', 72), ('MLN102-2', 54), ('MLN101-3', 36)]
2025-08-23 23:39:43,412 - INFO - 📄 处理发货数据文件: 2000026537789-data.xlsx
2025-08-23 23:39:43,422 - INFO -   📊 读取数据: 12 行, 7 列
2025-08-23 23:39:43,423 - INFO -   🔍 使用列: SKU='Артикул', 发货数量='В поставке (шт.)'
2025-08-23 23:39:43,423 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:39:43,423 - INFO -   📝 SKU示例: [('MLN101-1', 144), ('MLN101-3', 48), ('MLN101-4', 36)]
2025-08-23 23:39:43,424 - INFO - 📦 发货数据文件处理完成，总计 16 个唯一SKU
2025-08-23 23:39:43,424 - INFO - 
📋 开始处理 25 个验收报告文件...
2025-08-23 23:39:43,424 - INFO - 📄 处理验收报告文件: supply-2000024950553-acceptance-report.xlsx
2025-08-23 23:39:43,440 - INFO -   📊 读取数据: 17 行, 14 列
2025-08-23 23:39:43,441 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,441 - INFO -   ✅ 提取完成: 有效行=17, 无效行=0, 唯一SKU=17
2025-08-23 23:39:43,442 - INFO -   📝 SKU示例: [('MLN01-3', (32, 32)), ('MLN02-3', (20, 20)), ('MLN101-1', (576, 573))]
2025-08-23 23:39:43,442 - INFO - 📄 处理验收报告文件: supply-2000024950789-acceptance-report.xlsx
2025-08-23 23:39:43,453 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:39:43,453 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,454 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:39:43,454 - INFO -   📝 SKU示例: [('MLN101-5', (16, 16)), ('MLN101-3', (36, 36)), ('MLN101-1', (72, 72))]
2025-08-23 23:39:43,454 - INFO - 📄 处理验收报告文件: supply-2000024953446-acceptance-report.xlsx
2025-08-23 23:39:43,467 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:39:43,468 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,468 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:39:43,469 - INFO -   📝 SKU示例: [('MLN102-2', (36, 36)), ('MLN101-3', (60, 56)), ('MNT100-1', (64, 64))]
2025-08-23 23:39:43,469 - INFO - 📄 处理验收报告文件: supply-2000024953461-acceptance-report.xlsx
2025-08-23 23:39:43,481 - INFO -   📊 读取数据: 10 行, 14 列
2025-08-23 23:39:43,482 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,482 - INFO -   ✅ 提取完成: 有效行=10, 无效行=0, 唯一SKU=10
2025-08-23 23:39:43,482 - INFO -   📝 SKU示例: [('MLN01-1', (14, 14)), ('MLN102-3', (36, 36)), ('MLN102-4', (48, 48))]
2025-08-23 23:39:43,483 - INFO - 📄 处理验收报告文件: supply-2000024953511-acceptance-report.xlsx
2025-08-23 23:39:43,496 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:39:43,496 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,497 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:39:43,497 - INFO -   📝 SKU示例: [('MLN102-4', (24, 24)), ('MLN101-5', (24, 24)), ('MLN101-3', (24, 24))]
2025-08-23 23:39:43,497 - INFO - 📄 处理验收报告文件: supply-2000024953524-acceptance-report.xlsx
2025-08-23 23:39:43,509 - INFO -   📊 读取数据: 10 行, 14 列
2025-08-23 23:39:43,509 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,510 - INFO -   ✅ 提取完成: 有效行=10, 无效行=0, 唯一SKU=10
2025-08-23 23:39:43,510 - INFO -   📝 SKU示例: [('MLN102-2', (36, 36)), ('MLN101-5', (32, 32)), ('MLN101-6', (16, 16))]
2025-08-23 23:39:43,510 - INFO - 📄 处理验收报告文件: supply-2000024990796-acceptance-report.xlsx
2025-08-23 23:39:43,524 - INFO -   📊 读取数据: 13 行, 14 列
2025-08-23 23:39:43,524 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,525 - INFO -   ✅ 提取完成: 有效行=13, 无效行=0, 唯一SKU=13
2025-08-23 23:39:43,525 - INFO -   📝 SKU示例: [('MLN101-1', (72, 72)), ('MLN101-2', (16, 16)), ('ODD100-1', (12, 12))]
2025-08-23 23:39:43,526 - INFO - 📄 处理验收报告文件: supply-2000024992213-acceptance-report.xlsx
2025-08-23 23:39:43,539 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:39:43,539 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,540 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:39:43,540 - INFO -   📝 SKU示例: [('MLN101-3', (72, 72)), ('MLN01-1', (14, 0)), ('MLN102-4', (12, 12))]
2025-08-23 23:39:43,540 - INFO - 📄 处理验收报告文件: supply-2000024992484-acceptance-report.xlsx
2025-08-23 23:39:43,552 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:39:43,553 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,553 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:39:43,553 - INFO -   📝 SKU示例: [('MLN101-1', (72, 72)), ('MNT100-1', (24, 24)), ('MLN102-3', (24, 24))]
2025-08-23 23:39:43,554 - INFO - 📄 处理验收报告文件: supply-2000024993125-acceptance-report.xlsx
2025-08-23 23:39:43,567 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:39:43,567 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,568 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:39:43,568 - INFO -   📝 SKU示例: [('MLN102-2', (18, 18)), ('MLN101-5', (16, 16)), ('MLN101-3', (36, 36))]
2025-08-23 23:39:43,568 - INFO - 📄 处理验收报告文件: supply-2000024994651-acceptance-report.xlsx
2025-08-23 23:39:43,580 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:39:43,580 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,581 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:39:43,581 - INFO -   📝 SKU示例: [('MLN101-1', (36, 36)), ('MNT100-1', (8, 8)), ('MLN101-2', (18, 18))]
2025-08-23 23:39:43,581 - INFO - 📄 处理验收报告文件: supply-2000024995339-acceptance-report.xlsx
2025-08-23 23:39:43,592 - INFO -   📊 读取数据: 1 行, 14 列
2025-08-23 23:39:43,592 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,593 - INFO -   ✅ 提取完成: 有效行=1, 无效行=0, 唯一SKU=1
2025-08-23 23:39:43,593 - INFO -   📝 SKU示例: [('MLN108-1', (20, 20))]
2025-08-23 23:39:43,593 - INFO - 📄 处理验收报告文件: supply-2000025090484-acceptance-report.xlsx
2025-08-23 23:39:43,615 - INFO -   📊 读取数据: 9 行, 14 列
2025-08-23 23:39:43,615 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,616 - INFO -   ✅ 提取完成: 有效行=9, 无效行=0, 唯一SKU=9
2025-08-23 23:39:43,616 - INFO -   📝 SKU示例: [('MLN101-4', (24, 24)), ('MLN102-2', (54, 54)), ('MLN101-3', (60, 60))]
2025-08-23 23:39:43,616 - INFO - 📄 处理验收报告文件: supply-2000026412138-acceptance-report.xlsx
2025-08-23 23:39:43,631 - INFO -   📊 读取数据: 17 行, 14 列
2025-08-23 23:39:43,631 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,631 - INFO -   ✅ 提取完成: 有效行=17, 无效行=0, 唯一SKU=17
2025-08-23 23:39:43,632 - INFO -   📝 SKU示例: [('ODD100-1', (60, 60)), ('MLN102-1', (90, 90)), ('MLN101-1', (396, 396))]
2025-08-23 23:39:43,632 - INFO - 📄 处理验收报告文件: supply-2000026412754-acceptance-report.xlsx
2025-08-23 23:39:43,645 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:39:43,646 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,646 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:39:43,646 - INFO -   📝 SKU示例: [('MLN102-4', (36, 36)), ('MLN01-4', (8, 6)), ('ODD100-2', (12, 12))]
2025-08-23 23:39:43,646 - INFO - 📄 处理验收报告文件: supply-2000026441759-acceptance-report.xlsx
2025-08-23 23:39:43,658 - INFO -   📊 读取数据: 15 行, 14 列
2025-08-23 23:39:43,658 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,659 - INFO -   ✅ 提取完成: 有效行=15, 无效行=0, 唯一SKU=15
2025-08-23 23:39:43,659 - INFO -   📝 SKU示例: [('ODD100-1', (24, 24)), ('MNT101-1', (20, 20)), ('MLN101-3', (60, 60))]
2025-08-23 23:39:43,659 - INFO - 📄 处理验收报告文件: supply-2000026444252-acceptance-report.xlsx
2025-08-23 23:39:43,673 - INFO -   📊 读取数据: 15 行, 14 列
2025-08-23 23:39:43,673 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,674 - INFO -   ✅ 提取完成: 有效行=15, 无效行=0, 唯一SKU=15
2025-08-23 23:39:43,675 - INFO -   📝 SKU示例: [('ODD100-1', (60, 60)), ('MLN101-4', (12, 12)), ('MLN102-3', (48, 48))]
2025-08-23 23:39:43,675 - INFO - 📄 处理验收报告文件: supply-2000026444834-acceptance-report.xlsx
2025-08-23 23:39:43,686 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:39:43,687 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,687 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:39:43,688 - INFO -   📝 SKU示例: [('MLN01-3', (8, 8)), ('MLN01-4', (8, 8)), ('MNT101-1', (15, 15))]
2025-08-23 23:39:43,688 - INFO - 📄 处理验收报告文件: supply-2000026445022-acceptance-report.xlsx
2025-08-23 23:39:43,700 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:39:43,700 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,701 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:39:43,701 - INFO -   📝 SKU示例: [('MLN101-2', (18, 18)), ('MNT102-1', (12, 12)), ('MLN01-3', (8, 8))]
2025-08-23 23:39:43,702 - INFO - 📄 处理验收报告文件: supply-2000026445223-acceptance-report.xlsx
2025-08-23 23:39:43,715 - INFO -   📊 读取数据: 16 行, 14 列
2025-08-23 23:39:43,715 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,716 - INFO -   ✅ 提取完成: 有效行=16, 无效行=0, 唯一SKU=16
2025-08-23 23:39:43,716 - INFO -   📝 SKU示例: [('MNT102-1', (6, 6)), ('MLN01-4', (8, 8)), ('ODD100-2', (12, 11))]
2025-08-23 23:39:43,717 - INFO - 📄 处理验收报告文件: supply-2000026445560-acceptance-report.xlsx
2025-08-23 23:39:43,730 - INFO -   📊 读取数据: 13 行, 14 列
2025-08-23 23:39:43,730 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,731 - INFO -   ✅ 提取完成: 有效行=13, 无效行=0, 唯一SKU=13
2025-08-23 23:39:43,731 - INFO -   📝 SKU示例: [('MLN101-3', (24, 24)), ('MLN01-1', (14, 14)), ('MLN102-4', (36, 36))]
2025-08-23 23:39:43,731 - INFO - 📄 处理验收报告文件: supply-2000026445787-acceptance-report.xlsx
2025-08-23 23:39:43,744 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:39:43,745 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,745 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:39:43,745 - INFO -   📝 SKU示例: [('MLN01-2', (14, 14)), ('MLN102-2', (36, 36)), ('MLN101-3', (36, 36))]
2025-08-23 23:39:43,746 - INFO - 📄 处理验收报告文件: supply-2000026446399-acceptance-report.xlsx
2025-08-23 23:39:43,757 - INFO -   📊 读取数据: 4 行, 14 列
2025-08-23 23:39:43,758 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,758 - INFO -   ✅ 提取完成: 有效行=4, 无效行=0, 唯一SKU=4
2025-08-23 23:39:43,758 - INFO -   📝 SKU示例: [('MLN101-5', (16, 16)), ('MLN101-3', (12, 12)), ('MLN101-1', (72, 72))]
2025-08-23 23:39:43,758 - INFO - 📄 处理验收报告文件: supply-2000026446444-acceptance-report.xlsx
2025-08-23 23:39:43,772 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:39:43,772 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,773 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:39:43,773 - INFO -   📝 SKU示例: [('MLN102-3', (24, 24)), ('MNT101-2', (10, 10)), ('ODD100-1', (12, 12))]
2025-08-23 23:39:43,773 - INFO - 📄 处理验收报告文件: supply-2000026646108-acceptance-report.xlsx
2025-08-23 23:39:43,783 - INFO -   📊 读取数据: 1 行, 14 列
2025-08-23 23:39:43,784 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:39:43,784 - INFO -   ✅ 提取完成: 有效行=1, 无效行=0, 唯一SKU=1
2025-08-23 23:39:43,784 - INFO -   📝 SKU示例: [('MLN108-1', (40, 40))]
2025-08-23 23:39:43,784 - INFO - 📋 验收报告文件处理完成，总计 23 个唯一SKU
2025-08-23 23:39:43,785 - INFO - 
📊 生成汇总报告...
2025-08-23 23:39:43,785 - INFO - 总共发现 23 个唯一SKU
2025-08-23 23:39:43,792 - INFO - 💾 保存报告到: ozon_advanced_sku_report_20250823_233943.xlsx
2025-08-23 23:39:43,821 - INFO - ✅ 报告保存成功
