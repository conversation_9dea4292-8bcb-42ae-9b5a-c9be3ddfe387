2025-08-23 23:45:19,756 - INFO - 🚀 开始Ozon高级SKU差异分析...
2025-08-23 23:45:19,756 - INFO - 🔍 扫描目录: ozon 7月送仓
2025-08-23 23:45:19,757 - INFO - 📁 找到 29 个Excel文件
2025-08-23 23:45:19,757 - INFO - 📦 发货数据文件: 2000026537595-data.xlsx
2025-08-23 23:45:19,757 - INFO - 📦 发货数据文件: 2000026537789-data.xlsx
2025-08-23 23:45:19,757 - INFO - 📋 验收报告文件: supply-2000024950553-acceptance-report.xlsx
2025-08-23 23:45:19,757 - INFO - 📋 验收报告文件: supply-2000024950789-acceptance-report.xlsx
2025-08-23 23:45:19,757 - INFO - 📋 验收报告文件: supply-2000024953446-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000024953461-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000024953511-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000024953524-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000024990796-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000024992213-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000024992484-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000024993125-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000024994651-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000024995339-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000025090484-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000026412138-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000026412754-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000026441759-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000026444252-acceptance-report.xlsx
2025-08-23 23:45:19,762 - INFO - 📋 验收报告文件: supply-2000026444834-acceptance-report.xlsx
2025-08-23 23:45:19,763 - INFO - 📋 验收报告文件: supply-2000026445022-acceptance-report.xlsx
2025-08-23 23:45:19,763 - INFO - 📋 验收报告文件: supply-2000026445223-acceptance-report.xlsx
2025-08-23 23:45:19,763 - INFO - 📋 验收报告文件: supply-2000026445560-acceptance-report.xlsx
2025-08-23 23:45:19,763 - INFO - 📋 验收报告文件: supply-2000026445787-acceptance-report.xlsx
2025-08-23 23:45:19,763 - INFO - 📋 验收报告文件: supply-2000026446399-acceptance-report.xlsx
2025-08-23 23:45:19,763 - INFO - 📋 验收报告文件: supply-2000026446444-acceptance-report.xlsx
2025-08-23 23:45:19,763 - INFO - 📋 验收报告文件: supply-2000026646108-acceptance-report.xlsx
2025-08-23 23:45:19,763 - INFO - 📊 文件分类结果:
2025-08-23 23:45:19,763 - INFO -   - 发货数据文件: 2 个
2025-08-23 23:45:19,763 - INFO -   - 验收报告文件: 25 个
2025-08-23 23:45:19,763 - INFO - 
📦 开始处理 2 个发货数据文件...
2025-08-23 23:45:19,763 - INFO - 📄 处理发货数据文件: 2000026537595-data.xlsx
2025-08-23 23:45:20,015 - INFO -   📊 读取数据: 11 行, 7 列
2025-08-23 23:45:20,015 - INFO -   🔍 使用列: SKU='Артикул', 发货数量='В поставке (шт.)'
2025-08-23 23:45:20,015 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:45:20,015 - INFO -   📝 SKU示例: [('MLN101-1', 72), ('MLN102-2', 54), ('MLN101-3', 36)]
2025-08-23 23:45:20,015 - INFO - 📄 处理发货数据文件: 2000026537789-data.xlsx
2025-08-23 23:45:20,030 - INFO -   📊 读取数据: 12 行, 7 列
2025-08-23 23:45:20,031 - INFO -   🔍 使用列: SKU='Артикул', 发货数量='В поставке (шт.)'
2025-08-23 23:45:20,033 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:45:20,034 - INFO -   📝 SKU示例: [('MLN101-1', 144), ('MLN101-3', 48), ('MLN101-4', 36)]
2025-08-23 23:45:20,034 - INFO - 📦 发货数据文件处理完成，总计 16 个唯一SKU
2025-08-23 23:45:20,034 - INFO - 
📋 开始处理 25 个验收报告文件...
2025-08-23 23:45:20,034 - INFO - 📄 处理验收报告文件: supply-2000024950553-acceptance-report.xlsx
2025-08-23 23:45:20,051 - INFO -   📊 读取数据: 17 行, 14 列
2025-08-23 23:45:20,052 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,052 - INFO -   ✅ 提取完成: 有效行=17, 无效行=0, 唯一SKU=17
2025-08-23 23:45:20,052 - INFO -   📝 SKU示例: [('MLN01-3', (32, 32)), ('MLN02-3', (20, 20)), ('MLN101-1', (576, 573))]
2025-08-23 23:45:20,052 - INFO - 📄 处理验收报告文件: supply-2000024950789-acceptance-report.xlsx
2025-08-23 23:45:20,066 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:45:20,066 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,066 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:45:20,066 - INFO -   📝 SKU示例: [('MLN101-5', (16, 16)), ('MLN101-3', (36, 36)), ('MLN101-1', (72, 72))]
2025-08-23 23:45:20,066 - INFO - 📄 处理验收报告文件: supply-2000024953446-acceptance-report.xlsx
2025-08-23 23:45:20,078 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:45:20,079 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,079 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:45:20,079 - INFO -   📝 SKU示例: [('MLN102-2', (36, 36)), ('MLN101-3', (60, 56)), ('MNT100-1', (64, 64))]
2025-08-23 23:45:20,079 - INFO - 📄 处理验收报告文件: supply-2000024953461-acceptance-report.xlsx
2025-08-23 23:45:20,091 - INFO -   📊 读取数据: 10 行, 14 列
2025-08-23 23:45:20,091 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,091 - INFO -   ✅ 提取完成: 有效行=10, 无效行=0, 唯一SKU=10
2025-08-23 23:45:20,091 - INFO -   📝 SKU示例: [('MLN01-1', (14, 14)), ('MLN102-3', (36, 36)), ('MLN102-4', (48, 48))]
2025-08-23 23:45:20,092 - INFO - 📄 处理验收报告文件: supply-2000024953511-acceptance-report.xlsx
2025-08-23 23:45:20,103 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:45:20,103 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,104 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:45:20,104 - INFO -   📝 SKU示例: [('MLN102-4', (24, 24)), ('MLN101-5', (24, 24)), ('MLN101-3', (24, 24))]
2025-08-23 23:45:20,104 - INFO - 📄 处理验收报告文件: supply-2000024953524-acceptance-report.xlsx
2025-08-23 23:45:20,116 - INFO -   📊 读取数据: 10 行, 14 列
2025-08-23 23:45:20,116 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,117 - INFO -   ✅ 提取完成: 有效行=10, 无效行=0, 唯一SKU=10
2025-08-23 23:45:20,117 - INFO -   📝 SKU示例: [('MLN102-2', (36, 36)), ('MLN101-5', (32, 32)), ('MLN101-6', (16, 16))]
2025-08-23 23:45:20,117 - INFO - 📄 处理验收报告文件: supply-2000024990796-acceptance-report.xlsx
2025-08-23 23:45:20,131 - INFO -   📊 读取数据: 13 行, 14 列
2025-08-23 23:45:20,131 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,132 - INFO -   ✅ 提取完成: 有效行=13, 无效行=0, 唯一SKU=13
2025-08-23 23:45:20,132 - INFO -   📝 SKU示例: [('MLN101-1', (72, 72)), ('MLN101-2', (16, 16)), ('ODD100-1', (12, 12))]
2025-08-23 23:45:20,132 - INFO - 📄 处理验收报告文件: supply-2000024992213-acceptance-report.xlsx
2025-08-23 23:45:20,144 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:45:20,145 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,145 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:45:20,145 - INFO -   📝 SKU示例: [('MLN101-3', (72, 72)), ('MLN01-1', (14, 0)), ('MLN102-4', (12, 12))]
2025-08-23 23:45:20,145 - INFO - 📄 处理验收报告文件: supply-2000024992484-acceptance-report.xlsx
2025-08-23 23:45:20,158 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:45:20,158 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,158 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:45:20,158 - INFO -   📝 SKU示例: [('MLN101-1', (72, 72)), ('MNT100-1', (24, 24)), ('MLN102-3', (24, 24))]
2025-08-23 23:45:20,158 - INFO - 📄 处理验收报告文件: supply-2000024993125-acceptance-report.xlsx
2025-08-23 23:45:20,171 - INFO -   📊 读取数据: 7 行, 14 列
2025-08-23 23:45:20,171 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,172 - INFO -   ✅ 提取完成: 有效行=7, 无效行=0, 唯一SKU=7
2025-08-23 23:45:20,172 - INFO -   📝 SKU示例: [('MLN102-2', (18, 18)), ('MLN101-5', (16, 16)), ('MLN101-3', (36, 36))]
2025-08-23 23:45:20,172 - INFO - 📄 处理验收报告文件: supply-2000024994651-acceptance-report.xlsx
2025-08-23 23:45:20,194 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:45:20,194 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,195 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:45:20,195 - INFO -   📝 SKU示例: [('MLN101-1', (36, 36)), ('MNT100-1', (8, 8)), ('MLN101-2', (18, 18))]
2025-08-23 23:45:20,195 - INFO - 📄 处理验收报告文件: supply-2000024995339-acceptance-report.xlsx
2025-08-23 23:45:20,205 - INFO -   📊 读取数据: 1 行, 14 列
2025-08-23 23:45:20,205 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,205 - INFO -   ✅ 提取完成: 有效行=1, 无效行=0, 唯一SKU=1
2025-08-23 23:45:20,205 - INFO -   📝 SKU示例: [('MLN108-1', (20, 20))]
2025-08-23 23:45:20,205 - INFO - 📄 处理验收报告文件: supply-2000025090484-acceptance-report.xlsx
2025-08-23 23:45:20,219 - INFO -   📊 读取数据: 9 行, 14 列
2025-08-23 23:45:20,219 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,219 - INFO -   ✅ 提取完成: 有效行=9, 无效行=0, 唯一SKU=9
2025-08-23 23:45:20,219 - INFO -   📝 SKU示例: [('MLN101-4', (24, 24)), ('MLN102-2', (54, 54)), ('MLN101-3', (60, 60))]
2025-08-23 23:45:20,220 - INFO - 📄 处理验收报告文件: supply-2000026412138-acceptance-report.xlsx
2025-08-23 23:45:20,234 - INFO -   📊 读取数据: 17 行, 14 列
2025-08-23 23:45:20,234 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,234 - INFO -   ✅ 提取完成: 有效行=17, 无效行=0, 唯一SKU=17
2025-08-23 23:45:20,235 - INFO -   📝 SKU示例: [('ODD100-1', (60, 60)), ('MLN102-1', (90, 90)), ('MLN101-1', (396, 396))]
2025-08-23 23:45:20,235 - INFO - 📄 处理验收报告文件: supply-2000026412754-acceptance-report.xlsx
2025-08-23 23:45:20,254 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:45:20,254 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,255 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:45:20,255 - INFO -   📝 SKU示例: [('MLN102-4', (36, 36)), ('MLN01-4', (8, 6)), ('ODD100-2', (12, 12))]
2025-08-23 23:45:20,255 - INFO - 📄 处理验收报告文件: supply-2000026441759-acceptance-report.xlsx
2025-08-23 23:45:20,268 - INFO -   📊 读取数据: 15 行, 14 列
2025-08-23 23:45:20,274 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,274 - INFO -   ✅ 提取完成: 有效行=15, 无效行=0, 唯一SKU=15
2025-08-23 23:45:20,274 - INFO -   📝 SKU示例: [('ODD100-1', (24, 24)), ('MNT101-1', (20, 20)), ('MLN101-3', (60, 60))]
2025-08-23 23:45:20,274 - INFO - 📄 处理验收报告文件: supply-2000026444252-acceptance-report.xlsx
2025-08-23 23:45:20,288 - INFO -   📊 读取数据: 15 行, 14 列
2025-08-23 23:45:20,288 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,289 - INFO -   ✅ 提取完成: 有效行=15, 无效行=0, 唯一SKU=15
2025-08-23 23:45:20,289 - INFO -   📝 SKU示例: [('ODD100-1', (60, 60)), ('MLN101-4', (12, 12)), ('MLN102-3', (48, 48))]
2025-08-23 23:45:20,289 - INFO - 📄 处理验收报告文件: supply-2000026444834-acceptance-report.xlsx
2025-08-23 23:45:20,302 - INFO -   📊 读取数据: 11 行, 14 列
2025-08-23 23:45:20,302 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,302 - INFO -   ✅ 提取完成: 有效行=11, 无效行=0, 唯一SKU=11
2025-08-23 23:45:20,303 - INFO -   📝 SKU示例: [('MLN01-3', (8, 8)), ('MLN01-4', (8, 8)), ('MNT101-1', (15, 15))]
2025-08-23 23:45:20,303 - INFO - 📄 处理验收报告文件: supply-2000026445022-acceptance-report.xlsx
2025-08-23 23:45:20,316 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:45:20,316 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,316 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:45:20,316 - INFO -   📝 SKU示例: [('MLN101-2', (18, 18)), ('MNT102-1', (12, 12)), ('MLN01-3', (8, 8))]
2025-08-23 23:45:20,316 - INFO - 📄 处理验收报告文件: supply-2000026445223-acceptance-report.xlsx
2025-08-23 23:45:20,331 - INFO -   📊 读取数据: 16 行, 14 列
2025-08-23 23:45:20,331 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,332 - INFO -   ✅ 提取完成: 有效行=16, 无效行=0, 唯一SKU=16
2025-08-23 23:45:20,332 - INFO -   📝 SKU示例: [('MNT102-1', (6, 6)), ('MLN01-4', (8, 8)), ('ODD100-2', (12, 11))]
2025-08-23 23:45:20,332 - INFO - 📄 处理验收报告文件: supply-2000026445560-acceptance-report.xlsx
2025-08-23 23:45:20,344 - INFO -   📊 读取数据: 13 行, 14 列
2025-08-23 23:45:20,344 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,345 - INFO -   ✅ 提取完成: 有效行=13, 无效行=0, 唯一SKU=13
2025-08-23 23:45:20,345 - INFO -   📝 SKU示例: [('MLN101-3', (24, 24)), ('MLN01-1', (14, 14)), ('MLN102-4', (36, 36))]
2025-08-23 23:45:20,345 - INFO - 📄 处理验收报告文件: supply-2000026445787-acceptance-report.xlsx
2025-08-23 23:45:20,358 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:45:20,358 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,359 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:45:20,359 - INFO -   📝 SKU示例: [('MLN01-2', (14, 14)), ('MLN102-2', (36, 36)), ('MLN101-3', (36, 36))]
2025-08-23 23:45:20,359 - INFO - 📄 处理验收报告文件: supply-2000026446399-acceptance-report.xlsx
2025-08-23 23:45:20,370 - INFO -   📊 读取数据: 4 行, 14 列
2025-08-23 23:45:20,370 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,370 - INFO -   ✅ 提取完成: 有效行=4, 无效行=0, 唯一SKU=4
2025-08-23 23:45:20,370 - INFO -   📝 SKU示例: [('MLN101-5', (16, 16)), ('MLN101-3', (12, 12)), ('MLN101-1', (72, 72))]
2025-08-23 23:45:20,370 - INFO - 📄 处理验收报告文件: supply-2000026446444-acceptance-report.xlsx
2025-08-23 23:45:20,383 - INFO -   📊 读取数据: 12 行, 14 列
2025-08-23 23:45:20,383 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,383 - INFO -   ✅ 提取完成: 有效行=12, 无效行=0, 唯一SKU=12
2025-08-23 23:45:20,384 - INFO -   📝 SKU示例: [('MLN102-3', (24, 24)), ('MNT101-2', (10, 10)), ('ODD100-1', (12, 12))]
2025-08-23 23:45:20,384 - INFO - 📄 处理验收报告文件: supply-2000026646108-acceptance-report.xlsx
2025-08-23 23:45:20,394 - INFO -   📊 读取数据: 1 行, 14 列
2025-08-23 23:45:20,394 - INFO -   🔍 使用列: SKU='ШК', 发货数量='Заявлено (шт.)', 验收数量='Принято на склад (шт.)'
2025-08-23 23:45:20,394 - INFO -   ✅ 提取完成: 有效行=1, 无效行=0, 唯一SKU=1
2025-08-23 23:45:20,394 - INFO -   📝 SKU示例: [('MLN108-1', (40, 40))]
2025-08-23 23:45:20,395 - INFO - 📦 将验收报告中的发货数量合并到总发货数量中...
2025-08-23 23:45:20,395 - INFO - 📋 验收报告文件处理完成，总计 23 个唯一SKU
2025-08-23 23:45:20,395 - INFO - 📦 合并后总发货SKU数量: 23 个
2025-08-23 23:45:20,395 - INFO - 
📊 生成汇总报告...
2025-08-23 23:45:20,395 - INFO - 总共发现 23 个唯一SKU
2025-08-23 23:45:20,398 - INFO - 💾 保存报告到: ozon_advanced_sku_report_20250823_234520.xlsx
2025-08-23 23:45:20,424 - INFO - ✅ 报告保存成功
